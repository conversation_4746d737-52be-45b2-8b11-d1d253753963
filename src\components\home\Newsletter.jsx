import { useState } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { FaEnvelope, FaHeart } from 'react-icons/fa';

const NewsletterSection = styled.section`
  background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  padding: ${spacing['3xl']} ${spacing.xl};
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }
`;

const Container = styled.div`
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
`;

const Title = styled(motion.h2)`
  color: white;
  font-size: ${typography.fontSize['2xl']};
  margin-bottom: ${spacing.md};
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  
  @media (max-width: ${breakpoints.md}) {
    font-size: ${typography.fontSize.xl};
  }
`;

const Subtitle = styled(motion.p)`
  color: rgba(255,255,255,0.95);
  font-size: ${typography.fontSize.lg};
  margin-bottom: ${spacing.xl};
  line-height: 1.6;
  
  @media (max-width: ${breakpoints.md}) {
    font-size: ${typography.fontSize.md};
  }
`;

const Form = styled(motion.form)`
  display: flex;
  gap: ${spacing.sm};
  margin-bottom: ${spacing.lg};
  
  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
  }
`;

const EmailInput = styled.input`
  flex: 1;
  padding: ${spacing.md} ${spacing.lg};
  border: 3px solid rgba(255,255,255,0.3);
  border-radius: 25px;
  font-size: ${typography.fontSize.md};
  background: rgba(255,255,255,0.9);
  color: ${colors.neutral.dark};
  
  &::placeholder {
    color: ${colors.neutral.gray};
  }
  
  &:focus {
    outline: none;
    border-color: white;
    background: white;
    box-shadow: 0 0 20px rgba(255,255,255,0.5);
  }
`;

const SubmitButton = styled(motion.button)`
  padding: ${spacing.md} ${spacing.xl};
  background: linear-gradient(135deg, #FF69B4 0%, #32CD32 100%);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: ${typography.fontSize.md};
  font-weight: ${typography.fontWeight.bold};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  @media (max-width: ${breakpoints.sm}) {
    justify-content: center;
  }
`;

const SuccessMessage = styled(motion.div)`
  background: rgba(255,255,255,0.9);
  color: ${colors.success.dark};
  padding: ${spacing.md} ${spacing.lg};
  border-radius: 15px;
  font-weight: ${typography.fontWeight.medium};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${spacing.xs};
`;

const Benefits = styled(motion.div)`
  display: flex;
  justify-content: center;
  gap: ${spacing.lg};
  margin-top: ${spacing.lg};
  
  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
    gap: ${spacing.sm};
  }
`;

const Benefit = styled.div`
  color: rgba(255,255,255,0.9);
  font-size: ${typography.fontSize.sm};
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
`;

const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      setEmail('');
      
      // Reset success message after 3 seconds
      setTimeout(() => setIsSuccess(false), 3000);
    }, 1000);
  };

  return (
    <NewsletterSection>
      <Container>
        <Title
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          Join the Lumina Family! 💕
        </Title>
        
        <Subtitle
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
        >
          Get exclusive skincare tips, early access to new products, and special offers delivered to your inbox!
        </Subtitle>

        {isSuccess ? (
          <SuccessMessage
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <FaHeart /> Thank you for joining our community!
          </SuccessMessage>
        ) : (
          <Form
            onSubmit={handleSubmit}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <EmailInput
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <SubmitButton
              type="submit"
              disabled={isSubmitting}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FaEnvelope />
              {isSubmitting ? 'Joining...' : 'Join Now'}
            </SubmitButton>
          </Form>
        )}

        <Benefits
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <Benefit>✨ Exclusive Tips</Benefit>
          <Benefit>🎁 Special Offers</Benefit>
          <Benefit>🚀 Early Access</Benefit>
        </Benefits>
      </Container>
    </NewsletterSection>
  );
};

export default Newsletter;
