import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { PayPalButtons, PayPalScriptProvider } from "@paypal/react-paypal-js";
import {
  doc,
  setDoc,
  serverTimestamp,
  getDoc,
  collection,
  getDocs,
  addDoc,
  updateDoc,
} from "firebase/firestore";
import { db, functions } from "../firebase/config";
import { httpsCallable } from "firebase/functions";
import { useCart } from "../context/CartContext";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";
import styled from "styled-components";
import { colors, spacing, typography } from "../styles";

// Load Stripe outside of component to avoid recreating it on renders
console.log(
  "Loading Stripe with public key:",
  import.meta.env.VITE_STRIPE_PUBLIC_KEY
    ? "Key found (hidden for security)"
    : "No key found"
);
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// PayPal options
const paypalOptions = {
  "client-id": import.meta.env.VITE_PAYPAL_CLIENT_ID,
  currency: "USD",
  intent: "capture",
};

// Payment form component for Stripe
const PaymentForm = ({
  formData,
  cartItems,
  total,
  onSuccess,
  savePaymentMethod,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();
  const { darkMode } = useTheme();

  // Debug Stripe and Elements loading
  useEffect(() => {
    console.log(
      "PaymentForm mounted. Stripe ready:",
      !!stripe,
      "Elements ready:",
      !!elements
    );
  }, [stripe, elements]);

  // Custom styles for Stripe Elements
  const cardElementOptions = {
    style: {
      base: {
        color: darkMode ? "#FFFFFF" : "#32325d",
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: "antialiased",
        fontSize: "16px",
        "::placeholder": {
          color: darkMode ? "#AAAAAA" : "#aab7c4",
        },
      },
      invalid: {
        color: "#fa755a",
        iconColor: "#fa755a",
      },
    },
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      setError(
        "Payment system is not ready. Please wait a moment and try again."
      );
      return;
    }

    // Validate required form fields
    if (
      !formData.name ||
      !formData.email ||
      !formData.address ||
      !formData.city ||
      !formData.state ||
      !formData.zip
    ) {
      setError(
        "Please fill in all required shipping information before proceeding with payment."
      );
      return;
    }

    setProcessing(true);
    setError(null);

    console.log("Starting payment process with form data:", {
      name: formData.name,
      email: formData.email,
      address: formData.address,
      total: total,
      amount: Math.round(total * 100),
    });

    try {
      // Create payment intent via Firebase function
      console.log("Calling createPaymentIntent function...");
      const response = await fetch(
        "https://createpaymentintent-tqnc2ir54a-uc.a.run.app",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            amount: Math.round(total * 100), // Stripe requires amount in cents
            currency: "usd",
            customer_email: formData.email,
            save_payment_method: savePaymentMethod,
          }),
        }
      );

      console.log("Response status:", response.status);
      console.log("Response ok:", response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("HTTP error response:", errorText);
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${errorText}`
        );
      }

      const data = await response.json();
      console.log("Payment intent response data:", data);

      if (data.error) {
        setError(data.error.message);
        setProcessing(false);
        return;
      }

      // Confirm card payment
      console.log("Confirming card payment with Stripe...");
      const { error: stripeError, paymentIntent } =
        await stripe.confirmCardPayment(data.clientSecret, {
          payment_method: {
            card: elements.getElement(CardElement),
            billing_details: {
              name: formData.name,
              email: formData.email,
              address: {
                line1: formData.address,
                city: formData.city,
                state: formData.state,
                postal_code: formData.zip,
                country: formData.country,
              },
            },
          },
          setup_future_usage: savePaymentMethod ? "off_session" : undefined,
        });

      console.log("Stripe confirmation result:", {
        stripeError,
        paymentIntent,
      });

      if (stripeError) {
        console.error("Stripe error:", stripeError);
        setError(stripeError.message);
        setProcessing(false);
      } else if (paymentIntent.status === "succeeded") {
        console.log("Payment succeeded:", paymentIntent);
        // Save order to Firestore
        try {
          const orderId = await saveOrder(
            "stripe",
            paymentIntent.id,
            data.customerId
          );
          console.log("Order saved successfully with ID:", orderId);
          onSuccess(orderId);
        } catch (orderError) {
          console.error("Error saving order:", orderError);
          setError(
            "Payment was successful, but we couldn't create your order. Please contact support."
          );
          setProcessing(false);
        }
      } else {
        console.warn(
          "Payment intent returned with status:",
          paymentIntent.status
        );
        setError(
          "Payment processing returned an unexpected status. Please try again."
        );
        setProcessing(false);
      }
    } catch (err) {
      console.error("Payment error:", err);
      setError("An unexpected error occurred. Please try again.");
      setProcessing(false);
    }
  };

  // Save order to Firestore
  const saveOrder = async (paymentMethod, transactionId, customerId = null) => {
    console.log("Starting saveOrder function with:", {
      paymentMethod,
      transactionId,
      customerId,
    });

    try {
      const orderId = `order_${Date.now()}`;
      console.log("Generated order ID:", orderId);

      const orderData = {
        userId: currentUser?.uid || "guest",
        userEmail: formData.email,
        items: cartItems,
        shipping: {
          name: formData.name,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          zip: formData.zip,
          country: formData.country,
        },
        payment: {
          method: paymentMethod,
          transactionId: transactionId,
          customerId: customerId,
        },
        subtotal: total - 5.99 - total * 0.05,
        shipping: 5.99,
        tax: total * 0.05,
        total: total,
        status: "processing",
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      console.log("Order data prepared:", JSON.stringify(orderData, null, 2));

      // Save order to Firestore
      const orderRef = doc(db, "orders", orderId);
      await setDoc(orderRef, orderData);
      console.log("Order document created in Firestore");

      // Verify the order was saved correctly
      const savedOrder = await getDoc(orderRef);
      if (!savedOrder.exists()) {
        console.error(
          "Order verification failed - order not found in Firestore after saving"
        );
        throw new Error("Order verification failed");
      }
      console.log("Order verification successful");

      // Save address if requested
      if (currentUser && formData.saveAddress && selectedAddressId === "") {
        console.log("Saving address to account");
        await saveAddressToAccount();
      }

      // If this is a logged-in user, save the Stripe customer ID to their profile
      if (currentUser && customerId) {
        try {
          console.log("Saving Stripe customer ID to user profile");
          const userRef = doc(db, "users", currentUser.uid);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            // Update existing user document
            await updateDoc(userRef, {
              stripeCustomerId: customerId,
              updatedAt: serverTimestamp(),
            });
            console.log(
              "Updated existing user document with Stripe customer ID"
            );
          } else {
            // Create new user document
            await setDoc(userRef, {
              email: currentUser.email,
              displayName: currentUser.displayName || "",
              stripeCustomerId: customerId,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp(),
            });
            console.log("Created new user document with Stripe customer ID");
          }
        } catch (error) {
          console.error("Error saving Stripe customer ID:", error);
          // Continue with order processing even if this fails
        }
      }

      console.log("Order saved successfully, returning order ID:", orderId);
      return orderId;
    } catch (error) {
      console.error("Error in saveOrder function:", error);
      throw error; // Re-throw to be handled by the caller
    }
  };

  return (
    <div>
      <div
        className="card-element-container"
        style={{
          padding: "10px",
          border: `1px solid ${
            darkMode ? colors.neutral.gray : colors.neutral.light
          }`,
          borderRadius: "4px",
          marginBottom: "20px",
          backgroundColor: darkMode
            ? colors.neutral.darker
            : colors.neutral.white,
        }}
      >
        <CardElement options={cardElementOptions} />
      </div>

      {error && (
        <div
          style={{
            color: colors.error.main,
            marginBottom: "10px",
            padding: spacing.sm,
            backgroundColor: darkMode
              ? "rgba(220, 53, 69, 0.1)"
              : "rgba(220, 53, 69, 0.05)",
            borderRadius: "4px",
            fontWeight: typography.fontWeight.medium,
          }}
        >
          {error}
        </div>
      )}

      <SubmitButton
        type="button"
        onClick={handleSubmit}
        disabled={!stripe || processing}
      >
        {processing ? "Processing..." : "Pay Now"}
      </SubmitButton>
    </div>
  );
};

const CheckoutContainer = styled.div`
  padding: ${spacing.xl};
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${spacing.xl};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const CheckoutForm = styled.form`
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  padding: ${spacing.lg};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: ${spacing.md};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${spacing.xs};
  font-weight: ${typography.fontWeight.medium};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const Input = styled.input`
  width: 100%;
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};

  &::placeholder {
    color: ${(props) =>
      props.darkMode ? colors.neutral.gray : colors.neutral.darkGray};
  }

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.gray : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }

  option {
    background-color: ${(props) =>
      props.darkMode ? colors.neutral.dark : colors.neutral.white};
    color: ${(props) =>
      props.darkMode ? colors.neutral.white : colors.neutral.dark};
  }
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${spacing.md};

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const SubmitButton = styled.button`
  background-color: ${colors.primary.main};
  color: white;
  border: none;
  padding: ${spacing.md};
  font-size: ${typography.fontSize.md};
  font-weight: ${typography.fontWeight.medium};
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
  margin-top: ${spacing.md};

  &:hover {
    background-color: ${colors.primary.dark};
  }

  &:disabled {
    background-color: ${colors.neutral.gray};
    cursor: not-allowed;
  }
`;

const OrderSummary = styled.div`
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.light};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  padding: ${spacing.lg};
  border-radius: 8px;
  align-self: start;
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: ${spacing.sm};

  &:last-child {
    margin-top: ${spacing.md};
    padding-top: ${spacing.md};
    border-top: 1px solid
      ${(props) =>
        props.darkMode ? colors.neutral.darker : colors.neutral.gray};
    font-weight: ${typography.fontWeight.bold};
  }
`;

const SummaryItems = styled.div`
  margin: ${spacing.md} 0;
  max-height: 200px;
  overflow-y: auto;
`;

const SummaryItem = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: ${spacing.xs};
  font-size: ${typography.fontSize.sm};
`;

const LoginPrompt = styled.div`
  margin-bottom: ${spacing.md};
  padding: ${spacing.md};
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.lighter};
  border-radius: 4px;
`;

const LoginLink = styled.button`
  background: none;
  border: none;
  color: ${colors.primary.main};
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: inherit;

  &:hover {
    color: ${colors.primary.dark};
  }
`;

const Checkout = () => {
  const { darkMode } = useTheme();
  const { cartItems, cartTotal, clearCart } = useCart();
  const { currentUser, savePreviousLocation } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    name: currentUser?.displayName || "",
    email: currentUser?.email || "",
    address: "",
    city: "",
    state: "",
    zip: "",
    country: "US",
    paymentMethod: "credit",
    savePaymentMethod: false,
  });

  const [showLoginForm, setShowLoginForm] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState("");

  useEffect(() => {
    // Auto-populate form with user profile data if available
    const fetchUserProfile = async () => {
      if (currentUser) {
        try {
          const userDocRef = doc(db, "users", currentUser.uid);
          const userDoc = await getDoc(userDocRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setFormData((prevData) => ({
              ...prevData,
              name: currentUser.displayName || prevData.name,
              email: currentUser.email || prevData.email,
              phone: userData.phoneNumber || prevData.phone,
              // We don't auto-populate address here as that's handled in the addresses section
            }));
          }
        } catch (error) {
          console.error("Error fetching user profile:", error);
        }
      }
    };

    fetchUserProfile();
  }, [currentUser]);

  useEffect(() => {
    const fetchUserAddresses = async () => {
      if (currentUser) {
        try {
          const addressesRef = collection(
            db,
            "users",
            currentUser.uid,
            "addresses"
          );
          const addressesSnapshot = await getDocs(addressesRef);
          const addressesList = addressesSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }));

          setSavedAddresses(addressesList);

          // If user has a default address, select it automatically
          const defaultAddress = addressesList.find((addr) => addr.isDefault);
          if (defaultAddress) {
            setSelectedAddressId(defaultAddress.id);
            // Populate form with default address
            setFormData((prev) => ({
              ...prev,
              name: prev.name || defaultAddress.name,
              address: defaultAddress.street,
              city: defaultAddress.city,
              state: defaultAddress.state,
              zip: defaultAddress.zip,
              country: defaultAddress.country,
            }));
          }
        } catch (error) {
          console.error("Error fetching addresses:", error);
        }
      }
    };

    fetchUserAddresses();
  }, [currentUser]);

  const handleAddressSelect = (e) => {
    const addressId = e.target.value;
    setSelectedAddressId(addressId);

    if (addressId === "") {
      // Clear address fields if "Enter new address" is selected
      setFormData((prev) => ({
        ...prev,
        address: "",
        city: "",
        state: "",
        zip: "",
        country: "US",
      }));
      return;
    }

    // Find the selected address and populate form
    const selectedAddress = savedAddresses.find(
      (addr) => addr.id === addressId
    );
    if (selectedAddress) {
      setFormData((prev) => ({
        ...prev,
        name: prev.name || selectedAddress.name,
        address: selectedAddress.street,
        city: selectedAddress.city,
        state: selectedAddress.state,
        zip: selectedAddress.zip,
        country: selectedAddress.country,
      }));
    }
  };

  // Calculate order summary values
  const subtotal = cartTotal;
  const shipping = cartItems.length > 0 ? 5.99 : 0;
  const tax = subtotal * 0.05;
  const total = subtotal + shipping + tax;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Prevent the main form from submitting
    // Payment processing is handled by the PaymentForm component
    console.log("Main form submit prevented - payment handled by PaymentForm");
  };

  const handlePaymentSuccess = async (orderId) => {
    console.log("Payment success handler called with order ID:", orderId);

    try {
      // Verify the order exists in Firestore before clearing cart
      const orderRef = doc(db, "orders", orderId);
      const orderDoc = await getDoc(orderRef);

      if (!orderDoc.exists()) {
        console.error("Order not found in Firestore before redirect:", orderId);
        setError(
          "Order processing error. Please contact support with this reference: " +
            orderId
        );
        setProcessing(false);
        return;
      }

      console.log("Order verified before redirect:", orderId);

      // Send order confirmation email
      try {
        console.log("Sending order confirmation email...");
        const orderData = orderDoc.data();
        const sendOrderConfirmationEmail = httpsCallable(
          functions,
          "sendOrderConfirmationEmail"
        );

        await sendOrderConfirmationEmail({
          orderId: orderId,
          userEmail: orderData.userEmail,
          items: orderData.items,
          total: orderData.total,
          shipping: orderData.shipping,
        });

        console.log("Order confirmation email sent successfully");
      } catch (emailError) {
        console.error("Error sending order confirmation email:", emailError);
        // Don't block the order completion if email fails
      }

      // Mark any abandoned carts as recovered
      try {
        console.log("Marking abandoned carts as recovered...");
        const markCartRecovered = httpsCallable(functions, "markCartRecovered");

        await markCartRecovered({
          userEmail: orderData.userEmail,
          userId: orderData.userId !== "guest" ? orderData.userId : null,
        });

        console.log("Abandoned carts marked as recovered");
      } catch (recoveryError) {
        console.error("Error marking cart as recovered:", recoveryError);
        // Don't block the order completion if this fails
      }

      // Clear the cart AFTER verifying order exists
      clearCart();
      console.log("Cart cleared successfully");

      // Redirect to the correct success page with order ID
      console.log("Redirecting to success page with order ID:", orderId);
      navigate(`/order/success?orderId=${orderId}`);
    } catch (error) {
      console.error("Error in handlePaymentSuccess:", error);
      setError(
        "An error occurred after payment. Please check your order history or contact support."
      );
      setProcessing(false);
    }
  };

  const handleLoginPrompt = () => {
    // Save the current location before redirecting
    savePreviousLocation("/checkout");
    // Redirect to login page
    navigate("/login");
  };

  if (cartItems.length === 0) {
    return (
      <CheckoutContainer>
        <div>
          <h2>Your cart is empty</h2>
          <p>Please add some products to your cart before checking out.</p>
          <SubmitButton as="a" href="/shop">
            Continue Shopping
          </SubmitButton>
        </div>
      </CheckoutContainer>
    );
  }

  return (
    <CheckoutContainer>
      <CheckoutForm onSubmit={handleSubmit} darkMode={darkMode}>
        <h2>Checkout</h2>

        {!currentUser && !showLoginForm && (
          <LoginPrompt darkMode={darkMode}>
            <p>
              Already have an account?{" "}
              <LoginLink onClick={handleLoginPrompt}>Log in</LoginLink> for a
              faster checkout experience.
            </p>
            <p>Or continue as a guest below.</p>
          </LoginPrompt>
        )}

        <h3>Shipping Information</h3>
        {currentUser && savedAddresses.length > 0 && (
          <FormGroup>
            <Label htmlFor="savedAddress">Select a saved address</Label>
            <Select
              id="savedAddress"
              name="savedAddress"
              value={selectedAddressId}
              onChange={handleAddressSelect}
              darkMode={darkMode}
            >
              <option value="">Enter new address</option>
              {savedAddresses.map((address) => (
                <option key={address.id} value={address.id}>
                  {address.name} - {address.street}, {address.city},{" "}
                  {address.state} {address.zip}
                  {address.isDefault ? " (Default)" : ""}
                </option>
              ))}
            </Select>
          </FormGroup>
        )}
        {currentUser && selectedAddressId === "" && (
          <FormGroup>
            <Label style={{ display: "flex", alignItems: "center" }}>
              <input
                type="checkbox"
                id="saveAddress"
                name="saveAddress"
                checked={formData.saveAddress || false}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    saveAddress: e.target.checked,
                  }))
                }
                style={{ marginRight: spacing.xs }}
              />
              <span
                style={{
                  color: darkMode ? colors.neutral.white : colors.neutral.dark,
                }}
              >
                Save this address to your account
              </span>
            </Label>
          </FormGroup>
        )}
        <FormGroup>
          <Label htmlFor="name" darkMode={darkMode}>
            Full Name
          </Label>
          <Input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            darkMode={darkMode}
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="email" darkMode={darkMode}>
            Email Address
          </Label>
          <Input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            darkMode={darkMode}
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="address" darkMode={darkMode}>
            Street Address
          </Label>
          <Input
            type="text"
            id="address"
            name="address"
            value={formData.address}
            onChange={handleChange}
            required
            darkMode={darkMode}
          />
        </FormGroup>

        <FormRow>
          <FormGroup>
            <Label htmlFor="city" darkMode={darkMode}>
              City
            </Label>
            <Input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="state" darkMode={darkMode}>
              State/Province
            </Label>
            <Input
              type="text"
              id="state"
              name="state"
              value={formData.state}
              onChange={handleChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="zip" darkMode={darkMode}>
              ZIP/Postal Code
            </Label>
            <Input
              type="text"
              id="zip"
              name="zip"
              value={formData.zip}
              onChange={handleChange}
              required
              darkMode={darkMode}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="country" darkMode={darkMode}>
              Country
            </Label>
            <Select
              id="country"
              name="country"
              value={formData.country}
              onChange={handleChange}
              required
              darkMode={darkMode}
            >
              <option value="US">United States</option>
              <option value="CA">Canada</option>
              <option value="UK">United Kingdom</option>
              <option value="AU">Australia</option>
            </Select>
          </FormGroup>
        </FormRow>

        <h3>Payment Method</h3>
        <FormGroup>
          <Label htmlFor="paymentMethod" darkMode={darkMode}>
            Select Payment Method
          </Label>
          <Select
            id="paymentMethod"
            name="paymentMethod"
            value={formData.paymentMethod}
            onChange={handleChange}
            required
            darkMode={darkMode}
          >
            <option value="credit">Credit Card</option>
            <option value="paypal">PayPal</option>
          </Select>
        </FormGroup>

        {formData.paymentMethod === "credit" && (
          <>
            <FormGroup>
              <Label darkMode={darkMode}>Card Details</Label>
              <div
                style={{
                  padding: "10px",
                  border: `1px solid ${
                    darkMode ? colors.neutral.gray : colors.neutral.light
                  }`,
                  borderRadius: "4px",
                  backgroundColor: darkMode
                    ? colors.neutral.dark
                    : colors.neutral.white,
                }}
              >
                <Elements stripe={stripePromise}>
                  <PaymentForm
                    formData={formData}
                    cartItems={cartItems}
                    total={total}
                    onSuccess={handlePaymentSuccess}
                    savePaymentMethod={formData.savePaymentMethod}
                  />
                </Elements>
              </div>
            </FormGroup>

            {currentUser && (
              <FormGroup>
                <Label style={{ display: "flex", alignItems: "center" }}>
                  <input
                    type="checkbox"
                    id="savePaymentMethod"
                    name="savePaymentMethod"
                    checked={formData.savePaymentMethod}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        savePaymentMethod: e.target.checked,
                      }))
                    }
                    style={{ marginRight: spacing.xs }}
                  />
                  <span
                    style={{
                      color: darkMode
                        ? colors.neutral.white
                        : colors.neutral.dark,
                    }}
                  >
                    Save this payment method for future purchases
                  </span>
                </Label>
              </FormGroup>
            )}
          </>
        )}
      </CheckoutForm>

      <OrderSummary darkMode={darkMode}>
        <h3>Order Summary</h3>

        <SummaryItems>
          {cartItems.map((item) => (
            <SummaryItem key={item.id}>
              <span>
                {item.name} × {item.quantity}
              </span>
              <span>${(item.price * item.quantity).toFixed(2)}</span>
            </SummaryItem>
          ))}
        </SummaryItems>

        <SummaryRow darkMode={darkMode}>
          <span>Subtotal</span>
          <span>${subtotal.toFixed(2)}</span>
        </SummaryRow>
        <SummaryRow darkMode={darkMode}>
          <span>Shipping</span>
          <span>${shipping.toFixed(2)}</span>
        </SummaryRow>
        <SummaryRow darkMode={darkMode}>
          <span>Tax</span>
          <span>${tax.toFixed(2)}</span>
        </SummaryRow>
        <SummaryRow darkMode={darkMode}>
          <span>Total</span>
          <span>${total.toFixed(2)}</span>
        </SummaryRow>
      </OrderSummary>
    </CheckoutContainer>
  );
};

// Add this function to save a new address
const saveAddressToAccount = async () => {
  if (!currentUser || !formData.saveAddress) return;

  try {
    const addressData = {
      name: formData.name,
      street: formData.address,
      city: formData.city,
      state: formData.state,
      zip: formData.zip,
      country: formData.country,
      isDefault: savedAddresses.length === 0, // Make default if it's the first address
    };

    const addressesRef = collection(db, "users", currentUser.uid, "addresses");
    await addDoc(addressesRef, addressData);
  } catch (error) {
    console.error("Error saving address:", error);
  }
};

export default Checkout;
