import { motion } from 'framer-motion';
import styled from 'styled-components';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { FaGift, FaPercent, FaShippingFast, FaClock } from 'react-icons/fa';

const OffersSection = styled.section`
  padding: ${spacing['2xl']} ${spacing.xl};
  background: linear-gradient(135deg, #FFF0F5 0%, #F0FFF0 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,182,193,0.1) 0%, transparent 70%);
    animation: pulse 8s ease-in-out infinite;
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
  }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const Title = styled(motion.h2)`
  color: ${colors.neutral.dark};
  font-size: ${typography.fontSize['2xl']};
  margin-bottom: ${spacing.md};
  
  @media (max-width: ${breakpoints.md}) {
    font-size: ${typography.fontSize.xl};
  }
`;

const OffersGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${spacing.lg};
  
  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const OfferCard = styled(motion.div)`
  background: white;
  border-radius: 20px;
  padding: ${spacing.xl};
  text-align: center;
  box-shadow: 0 10px 30px rgba(255, 182, 193, 0.2);
  border: 2px solid #FFE4E1;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  }
`;

const OfferIcon = styled.div`
  width: 70px;
  height: 70px;
  margin: 0 auto ${spacing.md};
  border-radius: 50%;
  background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
`;

const OfferTitle = styled.h3`
  color: ${colors.neutral.dark};
  font-size: ${typography.fontSize.lg};
  margin-bottom: ${spacing.sm};
  font-weight: ${typography.fontWeight.bold};
`;

const OfferDescription = styled.p`
  color: ${colors.neutral.gray};
  font-size: ${typography.fontSize.md};
  margin-bottom: ${spacing.md};
  line-height: 1.6;
`;

const OfferCode = styled.div`
  background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  color: white;
  padding: ${spacing.sm} ${spacing.md};
  border-radius: 15px;
  font-weight: ${typography.fontWeight.bold};
  font-size: ${typography.fontSize.sm};
  display: inline-block;
  margin-bottom: ${spacing.md};
`;

const OfferExpiry = styled.div`
  color: ${colors.neutral.gray};
  font-size: ${typography.fontSize.xs};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${spacing.xs};
`;

const SpecialOffers = () => {
  const offers = [
    {
      icon: FaPercent,
      title: "First Order Discount",
      description: "Get 20% off your first order when you sign up for our newsletter!",
      code: "WELCOME20",
      expiry: "Valid for new customers only"
    },
    {
      icon: FaShippingFast,
      title: "Free Shipping",
      description: "Enjoy free shipping on all orders over $50. No code needed!",
      code: "AUTO APPLIED",
      expiry: "On orders $50+"
    },
    {
      icon: FaGift,
      title: "Bundle & Save",
      description: "Buy any 3 products and get the 4th one absolutely free!",
      code: "BUY3GET1",
      expiry: "Limited time offer"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <OffersSection>
      <Container>
        <SectionHeader>
          <Title
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Special Offers Just for You! 🎁
          </Title>
        </SectionHeader>

        <OffersGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {offers.map((offer, index) => (
            <OfferCard
              key={index}
              variants={cardVariants}
              whileHover={{ 
                y: -10, 
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <OfferIcon>
                <offer.icon />
              </OfferIcon>
              
              <OfferTitle>{offer.title}</OfferTitle>
              
              <OfferDescription>
                {offer.description}
              </OfferDescription>
              
              <OfferCode>
                {offer.code}
              </OfferCode>
              
              <OfferExpiry>
                <FaClock />
                {offer.expiry}
              </OfferExpiry>
            </OfferCard>
          ))}
        </OffersGrid>
      </Container>
    </OffersSection>
  );
};

export default SpecialOffers;
