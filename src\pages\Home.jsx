import { motion } from "framer-motion";
import styled from "styled-components";
import { useTheme } from "../context/ThemeContext";
import FeaturedProducts from "../components/home/<USER>";
import FeaturedBlogs from "../components/home/<USER>";
import VideoHero from "../components/home/<USER>";
import TrustIndicators from "../components/home/<USER>";
import FeaturedReviews from "../components/home/<USER>";
import Newsletter from "../components/home/<USER>";
import SpecialOffers from "../components/home/<USER>";
import { colors, spacing, typography } from "../styles";
import heroVideo from "../assets/videos/video.mp4";

const HeroSection = styled.section`
  background-color: ${colors.primary.light};
  padding: ${spacing["3xl"]} ${spacing.xl};
  text-align: center;
  position: relative;
  overflow: hidden;
`;

const HeroTitle = styled(motion.h1)`
  color: ${colors.neutral.dark};
  margin-bottom: ${spacing.md};
`;

const HeroSubtitle = styled(motion.p)`
  font-size: ${typography.fontSize.lg};
  max-width: 600px;
  margin: 0 auto ${spacing.xl};
`;

const FeaturedSection = styled.section`
  padding: ${spacing["2xl"]} ${spacing.xl};
`;

const SectionTitle = styled(motion.h2)`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const Home = () => {
  const { darkMode } = useTheme();

  return (
    <>
      <VideoHero
        videoSrc={heroVideo}
        fallbackImageSrc="/images/skincare-hero-fallback.jpg"
        title="Natural Beauty, Naturally You"
        subtitle="Discover our handcrafted skincare products made with premium natural ingredients for radiant, healthy skin."
      />

      <FeaturedProducts darkMode={darkMode} />
      <FeaturedBlogs darkMode={darkMode} />
      <FeaturedReviews darkMode={darkMode} />
      <SpecialOffers darkMode={darkMode} />
      <Newsletter darkMode={darkMode} />
      <TrustIndicators darkMode={darkMode} />
    </>
  );
};

export default Home;
