import { motion } from 'framer-motion';
import styled from 'styled-components';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { FaUsers, FaShippingFast, FaAward, FaHeart, FaShieldAlt, FaLeaf } from 'react-icons/fa';

const TrustSection = styled.section`
  padding: ${spacing['2xl']} ${spacing.xl};
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  border-top: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.lighter};
  border-bottom: 1px solid ${props => props.darkMode ? colors.neutral.gray : colors.neutral.lighter};
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const TrustGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${spacing.xl};
  
  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${spacing.lg};
  }
`;

const TrustItem = styled(motion.div)`
  text-align: center;
  padding: ${spacing.lg};
  border-radius: 15px;
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lighter};
  border: 2px solid transparent;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #FFB6C1;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 182, 193, 0.2);
  }
`;

const IconWrapper = styled.div`
  width: 60px;
  height: 60px;
  margin: 0 auto ${spacing.md};
  border-radius: 50%;
  background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
`;

const Number = styled.h3`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize.xl};
  font-weight: ${typography.fontWeight.bold};
  margin: 0 0 ${spacing.xs} 0;
`;

const Label = styled.p`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.gray};
  font-size: ${typography.fontSize.sm};
  margin: 0;
  font-weight: ${typography.fontWeight.medium};
`;

const TrustIndicators = ({ darkMode }) => {
  const indicators = [
    {
      icon: FaUsers,
      number: "10,000+",
      label: "Happy Customers"
    },
    {
      icon: FaShippingFast,
      number: "Free",
      label: "Shipping Over $50"
    },
    {
      icon: FaAward,
      number: "5 Star",
      label: "Average Rating"
    },
    {
      icon: FaHeart,
      number: "100%",
      label: "Cruelty Free"
    },
    {
      icon: FaShieldAlt,
      number: "30 Day",
      label: "Money Back Guarantee"
    },
    {
      icon: FaLeaf,
      number: "Natural",
      label: "Ingredients"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <TrustSection darkMode={darkMode}>
      <Container>
        <TrustGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {indicators.map((item, index) => (
            <TrustItem
              key={index}
              darkMode={darkMode}
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <IconWrapper>
                <item.icon />
              </IconWrapper>
              <Number darkMode={darkMode}>{item.number}</Number>
              <Label darkMode={darkMode}>{item.label}</Label>
            </TrustItem>
          ))}
        </TrustGrid>
      </Container>
    </TrustSection>
  );
};

export default TrustIndicators;
