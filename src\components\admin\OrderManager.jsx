import { useState, useEffect } from "react";
import { httpsCallable } from "firebase/functions";
import { functions, db } from "../../firebase/config";
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  doc,
  updateDoc,
} from "firebase/firestore";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../../styles";
import { useTheme } from "../../context/ThemeContext";
import { useAuth } from "../../context/AuthContext";
import {
  FaSearch,
  FaFilter,
  FaSort,
  FaDownload,
  FaEye,
  FaEdit,
  FaTrash,
} from "react-icons/fa";
import DeleteConfirmationModal from "../ui/DeleteConfirmationModal";
import { formatCurrency, formatDate } from "../../utils/formatters";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";

// Styled components
const Container = styled.div`
  padding: ${spacing.lg};
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;
  gap: ${spacing.sm};

  @media (max-width: ${breakpoints.md}) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const Title = styled.h2`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: ${spacing.sm};
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.sm}) {
    width: 100%;
    justify-content: space-between;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: ${spacing.md};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${spacing.sm} ${spacing.sm} ${spacing.sm} ${spacing.xl};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: ${spacing.md};
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 150px;
`;

const FilterLabel = styled.label`
  font-size: ${typography.fontSize.sm};
  margin-bottom: ${spacing.xs};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const Select = styled.select`
  padding: ${spacing.xs} ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  padding: ${spacing.xs} ${spacing.sm};
  background-color: ${(props) =>
    props.primary
      ? colors.primary.main
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.white};
  color: ${(props) =>
    props.primary
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: 1px solid
    ${(props) =>
      props.primary
        ? colors.primary.main
        : props.darkMode
        ? colors.neutral.dark
        : colors.neutral.light};
  border-radius: 4px;
  cursor: pointer;
  font-size: ${typography.fontSize.sm};

  &:hover {
    background-color: ${(props) =>
      props.primary
        ? colors.primary.dark
        : props.darkMode
        ? colors.neutral.darker
        : colors.neutral.lighter};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${spacing.lg};
`;

const Th = styled.th`
  text-align: left;
  padding: ${spacing.sm};
  border-bottom: 2px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-weight: ${typography.fontWeight.medium};
`;

const Td = styled.td`
  padding: ${spacing.sm};
  border-bottom: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 12px;
  font-size: ${typography.fontSize.xs};
  font-weight: ${typography.fontWeight.medium};
  text-transform: uppercase;
  background-color: ${(props) => {
    switch (props.status) {
      case "processing":
        return colors.info.light;
      case "shipped":
        return colors.warning.light;
      case "delivered":
        return colors.success.light;
      case "cancelled":
        return colors.error.light;
      case "refunded":
        return colors.neutral.light;
      default:
        return colors.neutral.light;
    }
  }};
  color: ${(props) => {
    switch (props.status) {
      case "processing":
        return colors.info.dark;
      case "shipped":
        return colors.warning.dark;
      case "delivered":
        return colors.success.dark;
      case "cancelled":
        return colors.error.dark;
      case "refunded":
        return colors.neutral.dark;
      default:
        return colors.neutral.dark;
    }
  }};
`;

const ActionButton = styled(Button)`
  padding: ${spacing.xs};
  margin-right: ${spacing.xs};
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${spacing.md};
`;

const PageInfo = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};
`;

const PageButtons = styled.div`
  display: flex;
  gap: ${spacing.xs};
`;

const PageButton = styled.button`
  padding: ${spacing.xs} ${spacing.sm};
  background-color: ${(props) =>
    props.active
      ? colors.primary.main
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.white};
  color: ${(props) =>
    props.active
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: 1px solid
    ${(props) =>
      props.active
        ? colors.primary.main
        : props.darkMode
        ? colors.neutral.dark
        : colors.neutral.light};
  border-radius: 4px;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.active
        ? colors.primary.dark
        : props.darkMode
        ? colors.neutral.darker
        : colors.neutral.lighter};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const OrderManager = () => {
  const { darkMode } = useTheme();
  const { currentUser, isAdmin, isEditor, refreshUserClaims } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [lastVisible, setLastVisible] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    dateRange: null,
    minTotal: null,
    maxTotal: null,
  });
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState("");
  const [trackingNumber, setTrackingNumber] = useState("");
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewOrder, setViewOrder] = useState(null);

  const ordersPerPage = 10;

  // Function to fetch orders with proper pagination
  const fetchOrders = async (page = currentPage) => {
    setLoading(true);
    setError(null);

    try {
      console.log("Admin fetching orders for page:", page);

      // First, get total count for pagination
      const ordersRef = collection(db, "orders");
      let countQuery = query(ordersRef);

      // Apply status filter for count
      if (filters.status && filters.status !== "all") {
        countQuery = query(countQuery, where("status", "==", filters.status));
      }

      // Get total count (for simple cases, we'll fetch all and count)
      const countSnapshot = await getDocs(countQuery);
      let allOrders = countSnapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate().toISOString(),
          updatedAt: data.updatedAt?.toDate().toISOString(),
        };
      });

      // Apply client-side filtering for search and date ranges
      if (searchQuery.trim()) {
        const searchLower = searchQuery.toLowerCase();
        allOrders = allOrders.filter((order) => {
          return (
            order.id.toLowerCase().includes(searchLower) ||
            order.userEmail?.toLowerCase().includes(searchLower) ||
            order.shipping?.name?.toLowerCase().includes(searchLower)
          );
        });
      }

      if (filters.dateFrom) {
        const fromDate = new Date(filters.dateFrom);
        allOrders = allOrders.filter(
          (order) => new Date(order.createdAt) >= fromDate
        );
      }

      if (filters.dateTo) {
        const toDate = new Date(filters.dateTo);
        toDate.setHours(23, 59, 59, 999);
        allOrders = allOrders.filter(
          (order) => new Date(order.createdAt) <= toDate
        );
      }

      if (filters.minTotal) {
        allOrders = allOrders.filter(
          (order) => order.total >= parseFloat(filters.minTotal)
        );
      }

      if (filters.maxTotal) {
        allOrders = allOrders.filter(
          (order) => order.total <= parseFloat(filters.maxTotal)
        );
      }

      // Apply sorting
      const sortField = sortBy === "total" ? "total" : "createdAt";
      allOrders.sort((a, b) => {
        let aVal = sortField === "total" ? a.total : new Date(a.createdAt);
        let bVal = sortField === "total" ? b.total : new Date(b.createdAt);

        if (sortDirection === "asc") {
          return aVal > bVal ? 1 : -1;
        } else {
          return aVal < bVal ? 1 : -1;
        }
      });

      // Calculate pagination
      const totalFilteredOrders = allOrders.length;
      const startIndex = (page - 1) * ordersPerPage;
      const endIndex = startIndex + ordersPerPage;
      const paginatedOrders = allOrders.slice(startIndex, endIndex);

      setOrders(paginatedOrders);
      setTotalOrders(totalFilteredOrders);
      setHasMore(endIndex < totalFilteredOrders);

      console.log(
        `Showing ${paginatedOrders.length} orders out of ${totalFilteredOrders} total`
      );
    } catch (err) {
      console.error("Error fetching orders:", err);
      setError("Failed to load orders. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (currentUser && (isAdmin || isEditor)) {
      setCurrentPage(1); // Reset to first page when filters change
      fetchOrders(1);
    }
  }, [currentUser, isAdmin, isEditor, sortBy, sortDirection, filters.status]);

  // Search effect with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentUser && (isAdmin || isEditor)) {
        setCurrentPage(1); // Reset to first page when searching
        fetchOrders(1);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Apply filters
  const applyFilters = () => {
    setCurrentPage(1);
    fetchOrders();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      status: "all",
      dateRange: null,
      minTotal: null,
      maxTotal: null,
    });
    setSearchQuery("");
    setCurrentPage(1);
    fetchOrders();
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortDirection("desc");
    }
    setCurrentPage(1);
  };

  // Handle status update - Using direct Firestore for better admin experience
  const updateOrderStatus = async (orderId, newStatus, trackingNum = null) => {
    setStatusUpdateLoading(true);
    try {
      // Ensure user is authenticated and has admin privileges
      if (!currentUser) {
        throw new Error("User not authenticated");
      }

      // Check admin permissions locally first
      if (!isAdmin && !isEditor) {
        throw new Error(
          "Insufficient permissions. Admin or editor role required."
        );
      }

      console.log("Updating order status:", {
        orderId,
        newStatus,
        trackingNum,
      });

      // Valid order statuses
      const validStatuses = [
        "processing",
        "shipped",
        "delivered",
        "cancelled",
        "refunded",
      ];
      if (!validStatuses.includes(newStatus)) {
        throw new Error(
          `Invalid status. Must be one of: ${validStatuses.join(", ")}`
        );
      }

      // Update order directly in Firestore (more reliable for admin operations)
      const orderRef = doc(db, "orders", orderId);
      const updateData = {
        status: newStatus,
        updatedAt: new Date(),
      };

      // Add tracking number if provided
      if (trackingNum && trackingNum.trim()) {
        updateData.trackingNumber = trackingNum.trim();
      }

      await updateDoc(orderRef, updateData);

      console.log("Order status updated successfully in Firestore");

      // Send status update email to customer (optional - can fail without breaking the update)
      try {
        const sendStatusEmail = httpsCallable(
          functions,
          "sendOrderStatusUpdateEmail"
        );
        await sendStatusEmail({
          orderId,
          status: newStatus,
          trackingNumber: trackingNum,
        });
        console.log("Status update email sent successfully");
      } catch (emailError) {
        console.warn(
          "Email sending failed, but order was updated:",
          emailError.message
        );
        // Don't throw error - order update succeeded
      }

      // Update local state immediately for better UX
      setOrders((prevOrders) =>
        prevOrders.map((order) =>
          order.id === orderId
            ? {
                ...order,
                status: newStatus,
                trackingNumber: trackingNum || order.trackingNumber,
                updatedAt: new Date().toISOString(),
              }
            : order
        )
      );

      // Show success message
      setError(null);
      console.log(`Order ${orderId} status updated to ${newStatus}`);
    } catch (err) {
      console.error("Error updating order status:", err);
      setError(`Failed to update order status: ${err.message}`);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Handle status modal
  const handleStatusUpdate = (order) => {
    setSelectedOrder(order);
    setNewStatus(order.status);
    setTrackingNumber(order.trackingNumber || "");
    setShowStatusModal(true);
  };

  const confirmStatusUpdate = async () => {
    if (!selectedOrder || !newStatus) return;

    try {
      await updateOrderStatus(selectedOrder.id, newStatus, trackingNumber);
      setShowStatusModal(false);
      setSelectedOrder(null);
      setNewStatus("");
      setTrackingNumber("");
    } catch (err) {
      console.error("Error updating status:", err);
    }
  };

  // Handle view order
  const handleViewOrder = (order) => {
    setViewOrder(order);
    setShowViewModal(true);
  };

  // Handle delete
  const handleDelete = (order) => {
    setSelectedOrder(order);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedOrder) return;

    setStatusUpdateLoading(true);
    try {
      // Import deleteDoc function
      const { deleteDoc, doc } = await import("firebase/firestore");

      // Delete order from Firestore
      await deleteDoc(doc(db, "orders", selectedOrder.id));

      // Update local state
      setOrders((prevOrders) =>
        prevOrders.filter((order) => order.id !== selectedOrder.id)
      );

      setShowDeleteModal(false);
      setSelectedOrder(null);

      console.log(`Order ${selectedOrder.id} deleted successfully`);
    } catch (err) {
      console.error("Error deleting order:", err);
      setError(`Failed to delete order: ${err.message}`);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Export orders to PDF
  const exportToPDF = () => {
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(18);
    doc.text("Orders Report", 14, 22);

    // Add date
    doc.setFontSize(11);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 14, 30);

    // Create table
    const tableColumn = ["Order ID", "Date", "Customer", "Status", "Total"];
    const tableRows = orders.map((order) => [
      order.id,
      formatDate(order.createdAt),
      order.userEmail,
      order.status,
      formatCurrency(order.total),
    ]);

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: "grid",
      styles: { fontSize: 9 },
      headStyles: { fillColor: [66, 66, 66] },
    });

    doc.save("orders-report.pdf");
  };

  return (
    <Container darkMode={darkMode}>
      <Header>
        <Title darkMode={darkMode}>Order Management</Title>
        <Controls>
          <Button darkMode={darkMode} onClick={exportToPDF}>
            <FaDownload /> Export
          </Button>
        </Controls>
      </Header>

      <SearchContainer>
        <SearchIcon darkMode={darkMode}>
          <FaSearch />
        </SearchIcon>
        <SearchInput
          type="text"
          placeholder="Search by order ID, customer name or email..."
          value={searchQuery}
          onChange={handleSearch}
          darkMode={darkMode}
        />
      </SearchContainer>

      <FiltersContainer>
        <FilterGroup>
          <FilterLabel darkMode={darkMode}>Status</FilterLabel>
          <Select
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
            darkMode={darkMode}
          >
            <option value="all">All Statuses</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel darkMode={darkMode}>Sort By</FilterLabel>
          <Select
            name="sortBy"
            value={sortBy}
            onChange={(e) => {
              setSortBy(e.target.value);
              setCurrentPage(1);
            }}
            darkMode={darkMode}
          >
            <option value="date">Date</option>
            <option value="total">Total</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel darkMode={darkMode}>Direction</FilterLabel>
          <Select
            name="sortDirection"
            value={sortDirection}
            onChange={(e) => {
              setSortDirection(e.target.value);
              setCurrentPage(1);
            }}
            darkMode={darkMode}
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </Select>
        </FilterGroup>

        <div style={{ display: "flex", alignItems: "flex-end" }}>
          <Button darkMode={darkMode} onClick={applyFilters}>
            <FaFilter /> Apply Filters
          </Button>
          <Button
            darkMode={darkMode}
            onClick={resetFilters}
            style={{ marginLeft: spacing.xs }}
          >
            Reset
          </Button>
        </div>
      </FiltersContainer>

      {loading ? (
        <EmptyState darkMode={darkMode}>Loading orders...</EmptyState>
      ) : error ? (
        <EmptyState darkMode={darkMode}>{error}</EmptyState>
      ) : orders.length === 0 ? (
        <EmptyState darkMode={darkMode}>No orders found</EmptyState>
      ) : (
        <>
          <div style={{ overflowX: "auto" }}>
            <Table>
              <thead>
                <tr>
                  <Th darkMode={darkMode}>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                      }}
                      onClick={() => handleSort("date")}
                    >
                      Order ID / Date
                      {sortBy === "date" && (
                        <FaSort
                          style={{
                            marginLeft: spacing.xs,
                            transform:
                              sortDirection === "asc"
                                ? "rotate(180deg)"
                                : "none",
                          }}
                        />
                      )}
                    </div>
                  </Th>
                  <Th darkMode={darkMode}>Customer</Th>
                  <Th darkMode={darkMode}>Items</Th>
                  <Th darkMode={darkMode}>Status</Th>
                  <Th darkMode={darkMode}>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                      }}
                      onClick={() => handleSort("total")}
                    >
                      Total
                      {sortBy === "total" && (
                        <FaSort
                          style={{
                            marginLeft: spacing.xs,
                            transform:
                              sortDirection === "asc"
                                ? "rotate(180deg)"
                                : "none",
                          }}
                        />
                      )}
                    </div>
                  </Th>
                  <Th darkMode={darkMode}>Actions</Th>
                </tr>
              </thead>
              <tbody>
                {orders.map((order) => (
                  <tr key={order.id}>
                    <Td darkMode={darkMode}>
                      <div>
                        <div>{order.id}</div>
                        <div
                          style={{
                            fontSize: typography.fontSize.sm,
                            color: darkMode
                              ? colors.neutral.light
                              : colors.neutral.gray,
                          }}
                        >
                          {formatDate(order.createdAt)}
                        </div>
                      </div>
                    </Td>
                    <Td darkMode={darkMode}>
                      <div>
                        <div>{order.shipping?.name}</div>
                        <div
                          style={{
                            fontSize: typography.fontSize.sm,
                            color: darkMode
                              ? colors.neutral.light
                              : colors.neutral.gray,
                          }}
                        >
                          {order.userEmail}
                        </div>
                      </div>
                    </Td>
                    <Td darkMode={darkMode}>
                      {order.items?.length || 0} items
                    </Td>
                    <Td darkMode={darkMode}>
                      <StatusBadge status={order.status}>
                        {order.status}
                      </StatusBadge>
                    </Td>
                    <Td darkMode={darkMode}>{formatCurrency(order.total)}</Td>
                    <Td darkMode={darkMode}>
                      <div style={{ display: "flex" }}>
                        <ActionButton
                          darkMode={darkMode}
                          title="View Details"
                          onClick={() => handleViewOrder(order)}
                        >
                          <FaEye />
                        </ActionButton>
                        <ActionButton
                          darkMode={darkMode}
                          title="Update Status"
                          onClick={() => handleStatusUpdate(order)}
                          disabled={statusUpdateLoading}
                        >
                          <FaEdit />
                        </ActionButton>
                        <ActionButton
                          darkMode={darkMode}
                          title="Delete Order"
                          onClick={() => handleDelete(order)}
                        >
                          <FaTrash />
                        </ActionButton>
                      </div>
                    </Td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>

          <Pagination>
            <PageInfo darkMode={darkMode}>
              Showing {(currentPage - 1) * ordersPerPage + 1} to{" "}
              {Math.min(currentPage * ordersPerPage, totalOrders)} of{" "}
              {totalOrders} orders
            </PageInfo>
            <PageButtons>
              <PageButton
                darkMode={darkMode}
                onClick={() => {
                  setCurrentPage(1);
                  fetchOrders(1);
                }}
                disabled={currentPage === 1}
              >
                First
              </PageButton>
              <PageButton
                darkMode={darkMode}
                onClick={() => {
                  const newPage = Math.max(currentPage - 1, 1);
                  setCurrentPage(newPage);
                  fetchOrders(newPage);
                }}
                disabled={currentPage === 1}
              >
                Previous
              </PageButton>
              <PageButton darkMode={darkMode} active>
                {currentPage}
              </PageButton>
              <PageButton
                darkMode={darkMode}
                onClick={() => {
                  const newPage = currentPage + 1;
                  setCurrentPage(newPage);
                  fetchOrders(newPage);
                }}
                disabled={!hasMore}
              >
                Next
              </PageButton>
            </PageButtons>
          </Pagination>
        </>
      )}

      {/* Status Update Modal */}
      {showStatusModal && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: darkMode
                ? colors.neutral.darker
                : colors.neutral.white,
              padding: spacing.lg,
              borderRadius: "8px",
              width: "90%",
              maxWidth: "500px",
              maxHeight: "80vh",
              overflow: "auto",
            }}
          >
            <h3
              style={{
                margin: "0 0 20px 0",
                color: darkMode ? colors.neutral.white : colors.neutral.darker,
              }}
            >
              Update Order Status
            </h3>

            <div style={{ marginBottom: spacing.md }}>
              <label
                style={{
                  display: "block",
                  marginBottom: spacing.xs,
                  color: darkMode ? colors.neutral.light : colors.neutral.gray,
                }}
              >
                Order ID: {selectedOrder?.id}
              </label>
            </div>

            <div style={{ marginBottom: spacing.md }}>
              <label
                style={{
                  display: "block",
                  marginBottom: spacing.xs,
                  color: darkMode ? colors.neutral.light : colors.neutral.gray,
                }}
              >
                Status:
              </label>
              <select
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value)}
                style={{
                  width: "100%",
                  padding: spacing.sm,
                  borderRadius: "4px",
                  border: `1px solid ${
                    darkMode ? colors.neutral.gray : colors.neutral.light
                  }`,
                  backgroundColor: darkMode
                    ? colors.neutral.dark
                    : colors.neutral.white,
                  color: darkMode
                    ? colors.neutral.white
                    : colors.neutral.darker,
                }}
              >
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>

            {(newStatus === "shipped" || newStatus === "delivered") && (
              <div style={{ marginBottom: spacing.md }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: spacing.xs,
                    color: darkMode
                      ? colors.neutral.light
                      : colors.neutral.gray,
                  }}
                >
                  Tracking Number (optional):
                </label>
                <input
                  type="text"
                  value={trackingNumber}
                  onChange={(e) => setTrackingNumber(e.target.value)}
                  placeholder="Enter tracking number"
                  style={{
                    width: "100%",
                    padding: spacing.sm,
                    borderRadius: "4px",
                    border: `1px solid ${
                      darkMode ? colors.neutral.gray : colors.neutral.light
                    }`,
                    backgroundColor: darkMode
                      ? colors.neutral.dark
                      : colors.neutral.white,
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                />
              </div>
            )}

            <div
              style={{
                display: "flex",
                gap: spacing.sm,
                justifyContent: "flex-end",
              }}
            >
              <Button
                darkMode={darkMode}
                onClick={() => {
                  setShowStatusModal(false);
                  setSelectedOrder(null);
                  setNewStatus("");
                  setTrackingNumber("");
                }}
                style={{ backgroundColor: colors.neutral.gray }}
              >
                Cancel
              </Button>
              <Button
                darkMode={darkMode}
                onClick={confirmStatusUpdate}
                disabled={statusUpdateLoading}
              >
                {statusUpdateLoading ? "Updating..." : "Update Status"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* View Order Modal */}
      {showViewModal && viewOrder && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: darkMode
                ? colors.neutral.darker
                : colors.neutral.white,
              padding: spacing.lg,
              borderRadius: "8px",
              width: "90%",
              maxWidth: "800px",
              maxHeight: "80vh",
              overflow: "auto",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: spacing.lg,
              }}
            >
              <h3
                style={{
                  margin: 0,
                  color: darkMode
                    ? colors.neutral.white
                    : colors.neutral.darker,
                }}
              >
                Order Details - #{viewOrder.id}
              </h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setViewOrder(null);
                }}
                style={{
                  background: "none",
                  border: "none",
                  fontSize: "24px",
                  cursor: "pointer",
                  color: darkMode ? colors.neutral.light : colors.neutral.gray,
                }}
              >
                ×
              </button>
            </div>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: spacing.md,
                marginBottom: spacing.lg,
              }}
            >
              <div>
                <h4
                  style={{
                    margin: "0 0 10px 0",
                    color: darkMode
                      ? colors.neutral.light
                      : colors.neutral.gray,
                  }}
                >
                  Customer Information
                </h4>
                <p
                  style={{
                    margin: "5px 0",
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                >
                  <strong>Name:</strong> {viewOrder.shipping?.name || "N/A"}
                </p>
                <p
                  style={{
                    margin: "5px 0",
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                >
                  <strong>Email:</strong> {viewOrder.userEmail}
                </p>
                <p
                  style={{
                    margin: "5px 0",
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                >
                  <strong>Address:</strong> {viewOrder.shipping?.address},{" "}
                  {viewOrder.shipping?.city}, {viewOrder.shipping?.state}{" "}
                  {viewOrder.shipping?.zip}
                </p>
              </div>

              <div>
                <h4
                  style={{
                    margin: "0 0 10px 0",
                    color: darkMode
                      ? colors.neutral.light
                      : colors.neutral.gray,
                  }}
                >
                  Order Information
                </h4>
                <p
                  style={{
                    margin: "5px 0",
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                >
                  <strong>Status:</strong>{" "}
                  <StatusBadge status={viewOrder.status}>
                    {viewOrder.status}
                  </StatusBadge>
                </p>
                <p
                  style={{
                    margin: "5px 0",
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                >
                  <strong>Date:</strong> {formatDate(viewOrder.createdAt)}
                </p>
                <p
                  style={{
                    margin: "5px 0",
                    color: darkMode
                      ? colors.neutral.white
                      : colors.neutral.darker,
                  }}
                >
                  <strong>Total:</strong> {formatCurrency(viewOrder.total)}
                </p>
                {viewOrder.trackingNumber && (
                  <p
                    style={{
                      margin: "5px 0",
                      color: darkMode
                        ? colors.neutral.white
                        : colors.neutral.darker,
                    }}
                  >
                    <strong>Tracking:</strong> {viewOrder.trackingNumber}
                  </p>
                )}
              </div>
            </div>

            <div>
              <h4
                style={{
                  margin: "0 0 15px 0",
                  color: darkMode ? colors.neutral.light : colors.neutral.gray,
                }}
              >
                Order Items
              </h4>
              <div
                style={{
                  border: `1px solid ${
                    darkMode ? colors.neutral.gray : colors.neutral.light
                  }`,
                  borderRadius: "4px",
                }}
              >
                {viewOrder.items?.map((item, index) => (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      padding: spacing.sm,
                      borderBottom:
                        index < viewOrder.items.length - 1
                          ? `1px solid ${
                              darkMode
                                ? colors.neutral.gray
                                : colors.neutral.light
                            }`
                          : "none",
                    }}
                  >
                    <div>
                      <p
                        style={{
                          margin: "0 0 5px 0",
                          fontWeight: "bold",
                          color: darkMode
                            ? colors.neutral.white
                            : colors.neutral.darker,
                        }}
                      >
                        {item.name}
                      </p>
                      <p
                        style={{
                          margin: 0,
                          fontSize: "14px",
                          color: darkMode
                            ? colors.neutral.light
                            : colors.neutral.gray,
                        }}
                      >
                        Quantity: {item.quantity} × {formatCurrency(item.price)}
                      </p>
                    </div>
                    <p
                      style={{
                        margin: 0,
                        fontWeight: "bold",
                        color: darkMode
                          ? colors.neutral.white
                          : colors.neutral.darker,
                      }}
                    >
                      {formatCurrency(item.price * item.quantity)}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: spacing.lg,
              }}
            >
              <Button
                darkMode={darkMode}
                onClick={() => {
                  setShowViewModal(false);
                  setViewOrder(null);
                }}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedOrder(null);
        }}
        onConfirm={confirmDelete}
        itemName={`order ${selectedOrder?.id}`}
        darkMode={darkMode}
      />
    </Container>
  );
};

export default OrderManager;
