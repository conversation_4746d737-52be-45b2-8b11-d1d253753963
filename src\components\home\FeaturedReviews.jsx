import { motion } from 'framer-motion';
import styled from 'styled-components';
import { colors, spacing, typography, breakpoints } from '../../styles';
import { FaStar, FaQuoteLeft } from 'react-icons/fa';

const ReviewsSection = styled.section`
  padding: ${spacing['3xl']} ${spacing.xl};
  background: ${props => props.darkMode ? colors.neutral.darker : colors.neutral.lighter};
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: ${spacing['2xl']};
`;

const Title = styled(motion.h2)`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-size: ${typography.fontSize['2xl']};
  margin-bottom: ${spacing.md};
  
  @media (max-width: ${breakpoints.md}) {
    font-size: ${typography.fontSize.xl};
  }
`;

const Subtitle = styled(motion.p)`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.gray};
  font-size: ${typography.fontSize.lg};
  max-width: 600px;
  margin: 0 auto;
`;

const ReviewsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: ${spacing.xl};
  
  @media (max-width: ${breakpoints.sm}) {
    grid-template-columns: 1fr;
    gap: ${spacing.lg};
  }
`;

const ReviewCard = styled(motion.div)`
  background: ${props => props.darkMode ? colors.neutral.dark : colors.neutral.white};
  border-radius: 20px;
  padding: ${spacing.xl};
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  border: 2px solid ${props => props.darkMode ? colors.neutral.gray : '#FFE4E1'};
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  }
`;

const QuoteIcon = styled.div`
  color: #FFB6C1;
  font-size: 24px;
  margin-bottom: ${spacing.md};
  opacity: 0.7;
`;

const ReviewText = styled.p`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: ${typography.fontSize.md};
  line-height: 1.7;
  margin-bottom: ${spacing.lg};
  font-style: italic;
`;

const StarRating = styled.div`
  display: flex;
  gap: 2px;
  margin-bottom: ${spacing.md};
`;

const Star = styled(FaStar)`
  color: #FFD700;
  font-size: 16px;
`;

const ReviewerInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
`;

const ReviewerAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFB6C1 0%, #98FB98 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${typography.fontWeight.bold};
  font-size: ${typography.fontSize.lg};
`;

const ReviewerDetails = styled.div``;

const ReviewerName = styled.h4`
  color: ${props => props.darkMode ? colors.neutral.white : colors.neutral.dark};
  margin: 0 0 4px 0;
  font-size: ${typography.fontSize.md};
  font-weight: ${typography.fontWeight.medium};
`;

const ReviewerLocation = styled.p`
  color: ${props => props.darkMode ? colors.neutral.light : colors.neutral.gray};
  margin: 0;
  font-size: ${typography.fontSize.sm};
`;

const ProductMention = styled.span`
  color: #FF69B4;
  font-weight: ${typography.fontWeight.medium};
`;

const FeaturedReviews = ({ darkMode }) => {
  const reviews = [
    {
      id: 1,
      text: "I've been using the Vitamin C Serum for 3 months and my skin has never looked better! The glow is real and my dark spots are fading beautifully.",
      rating: 5,
      reviewer: "Sarah M.",
      location: "New York, NY",
      avatar: "S",
      product: "Vitamin C Serum"
    },
    {
      id: 2,
      text: "The Hydrating Night Cream is absolutely divine! My skin feels so soft and plump in the morning. Worth every penny!",
      rating: 5,
      reviewer: "Emily R.",
      location: "Los Angeles, CA", 
      avatar: "E",
      product: "Hydrating Night Cream"
    },
    {
      id: 3,
      text: "Finally found a cleanser that doesn't strip my sensitive skin! The Gentle Foam Cleanser is perfect for my daily routine.",
      rating: 5,
      reviewer: "Jessica L.",
      location: "Chicago, IL",
      avatar: "J",
      product: "Gentle Foam Cleanser"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <ReviewsSection darkMode={darkMode}>
      <Container>
        <SectionHeader>
          <Title
            darkMode={darkMode}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            What Our Customers Say ✨
          </Title>
          <Subtitle
            darkMode={darkMode}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Real reviews from real customers who love their Lumina skincare journey
          </Subtitle>
        </SectionHeader>

        <ReviewsGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {reviews.map((review) => (
            <ReviewCard
              key={review.id}
              darkMode={darkMode}
              variants={cardVariants}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <QuoteIcon>
                <FaQuoteLeft />
              </QuoteIcon>
              
              <StarRating>
                {[...Array(review.rating)].map((_, i) => (
                  <Star key={i} />
                ))}
              </StarRating>
              
              <ReviewText darkMode={darkMode}>
                "{review.text.replace(review.product, `<ProductMention>${review.product}</ProductMention>`)}"
              </ReviewText>
              
              <ReviewerInfo>
                <ReviewerAvatar>
                  {review.avatar}
                </ReviewerAvatar>
                <ReviewerDetails>
                  <ReviewerName darkMode={darkMode}>
                    {review.reviewer}
                  </ReviewerName>
                  <ReviewerLocation darkMode={darkMode}>
                    {review.location}
                  </ReviewerLocation>
                </ReviewerDetails>
              </ReviewerInfo>
            </ReviewCard>
          ))}
        </ReviewsGrid>
      </Container>
    </ReviewsSection>
  );
};

export default FeaturedReviews;
